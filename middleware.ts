import { withAuth } from 'next-auth/middleware';
import { NextResponse } from 'next/server';
import { EUserRole } from '@/config/enums/user';
import { ERoutes } from '@/config/enums/enum';

/**
 * List of paths that should be exempt from INDEPENDENT_TEACHER smart redirects
 * These paths should not trigger redirects to /onboarding or /my-school
 */
const EXEMPTED_PATHS = [
  // API routes
  '/api',
  // Auth routes
  '/auth',
  // Onboarding routes (allow access to complete the flow)
  '/onboarding',
  // User profile and settings
  '/profile',
  '/settings',
  // Logout functionality
  '/logout',
  // Public assets and static files (handled by matcher but included for completeness)
  '/_next',
  '/favicon.ico',
  '/assets',
  '/public',
  // Other authenticated pages that shouldn't trigger school redirects
  '/manage-worksheet',
  '/users-management', // Only accessible to ADMIN anyway
  '/teacher-management', // Only accessible to SCHOOL_MANAGER anyway
  '/school-management', // General school management (different from personal school)
];

/**
 * Checks if a path should be exempt from INDEPENDENT_TEACHER smart redirects
 * @param path - The path to check
 * @returns true if the path should be exempt from redirects
 */
function isExemptedPath(path: string): boolean {
  return EXEMPTED_PATHS.some(exemptedPath => path.startsWith(exemptedPath));
}

/**
 * Logs unauthorized access attempts for security monitoring
 */
function logUnauthorizedAccess(
  userId: string | undefined,
  userRole: string | undefined,
  attemptedPath: string,
  userAgent: string | null | undefined,
  ip: string | null | undefined
) {
  const logData = {
    timestamp: new Date().toISOString(),
    event: 'UNAUTHORIZED_ACCESS_ATTEMPT',
    userId: userId || 'unknown',
    userRole: userRole || 'unknown',
    attemptedPath,
    userAgent: userAgent || 'unknown',
    ip: ip || 'unknown',
  };
}

export default withAuth(
  async function middleware(req) {
    const token = req.nextauth.token;
    const path = new URL(req.url).pathname;

    // Create a response object
    const response = NextResponse.next();

    // Set the custom header with the current path
    response.headers.set('x-current-path', path);

    // Check role-based access for protected routes
    if (token?.isLogin) {
      const userRole = token.role as string | undefined;
      const userId = token.id as string | undefined;
      const userSchoolId = token.schoolId as string | null | undefined;
      const userAgent = req.headers.get('user-agent');
      const ip = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';

      // TASK 9: Smart Redirects for INDEPENDENT_TEACHER Role
      if (userRole === EUserRole.INDEPENDENT_TEACHER && !isExemptedPath(path)) {
        // Debug logging
        console.log(`🔍 Middleware check - User: ${userId}, Role: ${userRole}, SchoolId: ${userSchoolId}, Path: ${path}`);

        // Redirect independent teachers from root path to manage-worksheet
        if (path === ERoutes.HOME) {
          // Refresh the router to ensure layout re-renders with updated session
          return NextResponse.redirect(new URL(`${ERoutes.MANAGE_WORKSHEET}`, req.url));
        }

        // Allow users to access the app immediately after signup without forced redirects
        // School setup is now fully optional and integrated into dashboard experience
        if (!userSchoolId) {
          // User has no school - allow access to all pages, no forced redirects
          console.log(`ℹ️ INDEPENDENT_TEACHER without school accessing ${path} - school setup is optional and integrated`);

          // Redirect legacy create-school path to my-school for integrated experience
          if (path === ERoutes.CREATE_SCHOOL) {
            console.log(`🔄 Redirecting INDEPENDENT_TEACHER from legacy create-school to integrated my-school`);
            return NextResponse.redirect(new URL(ERoutes.MY_SCHOOL, req.url));
          }
        } else {
          // User has school - allow access to all pages
          console.log(`✅ INDEPENDENT_TEACHER has school (${userSchoolId}), allowing access to ${path}`);

          // Redirect legacy create-school path to my-school
          if (path === ERoutes.CREATE_SCHOOL) {
            console.log(`🔄 Redirecting INDEPENDENT_TEACHER with school from ${path} to ${ERoutes.MY_SCHOOL}`);
            return NextResponse.redirect(new URL(ERoutes.MY_SCHOOL, req.url));
          }
        }
      }

      // Check access to /users-management routes (ADMIN only)
      if (path.startsWith('/users-management') && userRole !== EUserRole.ADMIN) {
        // Log unauthorized access attempt
        logUnauthorizedAccess(userId, userRole, path, userAgent, ip);
        return NextResponse.redirect(new URL('/', req.url));
      }

      // Check access to /manage-worksheet routes (TEACHER, SCHOOL_MANAGER, or INDEPENDENT_TEACHER)
      if (
        path.startsWith('/manage-worksheet') &&
        !(userRole === EUserRole.TEACHER ||
          userRole === EUserRole.SCHOOL_MANAGER ||
          userRole === EUserRole.INDEPENDENT_TEACHER)
      ) {
        // Log unauthorized access attempt
        logUnauthorizedAccess(userId, userRole, path, userAgent, ip);
        return NextResponse.redirect(new URL('/', req.url));
      }

      // Check access to /teacher-management routes (SCHOOL_MANAGER only - INDEPENDENT_TEACHER blocked)
      if (path.startsWith('/teacher-management') && userRole !== EUserRole.SCHOOL_MANAGER) {
        // Log unauthorized access attempt for INDEPENDENT_TEACHER specifically
        if (userRole === EUserRole.INDEPENDENT_TEACHER) {
          logUnauthorizedAccess(userId, userRole, path, userAgent, ip);
        }
        return NextResponse.redirect(new URL('/', req.url));
      }
    }

    // Log for debugging
    // console.log('Path:', path);
    // console.log('Token:', token);

    // Return the response
    return response;
  },
  {
    callbacks: {
      authorized: ({ req, token }) => {
        const path = req.nextUrl.pathname;

        // Allow access to /auth routes
        if (path.startsWith('/auth')) {
          return true;
        }

        // Only allow access if the token indicates the user is logged in
        return !!token?.isLogin;
      },
    },
  }
);

export const config = {
  // Matcher ignoring `/_next/` and `/api/`
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico|assets|public).*)'],
};
