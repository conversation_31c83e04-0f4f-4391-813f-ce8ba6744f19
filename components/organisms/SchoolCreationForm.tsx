'use client';

import React, { useState } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useSession } from 'next-auth/react';
import { handleCreateSchoolAction } from '@/actions/school.action';
import { createSchoolFormSchema, CreateSchoolFormData } from '@/lib/validators/school.validator';
import { Icon, Button } from '@/components/atoms';
import { cn } from '@/utils/cn';

// Props interface for the component
interface SchoolCreationFormProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export const SchoolCreationForm: React.FC<SchoolCreationFormProps> = ({
  onSuccess,
  onError
}) => {
  const { update: updateSession } = useSession();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<CreateSchoolFormData>({
    resolver: zodResolver(createSchoolFormSchema),
  });

  const onSubmit: SubmitHandler<CreateSchoolFormData> = async (data) => {
    setIsSubmitting(true);
    setSuccessMessage(null);
    setErrorMessage(null);

    try {
      // Call the server action with the form data
      const result = await handleCreateSchoolAction({
        name: data.name,
        address: data.address || '',
        phoneNumber: data.phoneNumber || '',
        registeredNumber: data.registeredNumber || '', // Required by API but not in task spec
        email: data.email || '',
      });

      if (result.status === 'success') {
        setSuccessMessage('School created successfully!');
        reset(); // Reset the form on success

        // Update session with the new school information
        if (result.data && updateSession) {
          try {
            await updateSession({
              user: {
                schoolId: result.data.id,
                school: {
                  id: result.data.id,
                  name: result.data.name,
                  address: result.data.address,
                  phoneNumber: result.data.phoneNumber,
                  registeredNumber: result.data.registeredNumber,
                  email: result.data.email,
                  brand: result.data.brand,
                }
              }
            });

            // Force a small delay to ensure session is propagated
            await new Promise(resolve => setTimeout(resolve, 500));
          } catch (error) {}
        }

        // Call onSuccess callback if provided
        if (onSuccess) {
          setTimeout(() => {
            onSuccess();
          }, 1500); // Give time to show success message
        }
      } else {
        // Handle error response
        const errorMsg = Array.isArray(result.message)
          ? result.message.map((m: any) =>
              typeof m === 'object' && m.message ? m.message : String(m)
            ).join(', ')
          : String(result.message || 'Failed to create school');
        setErrorMessage(errorMsg);

        // Call onError callback if provided
        if (onError) {
          onError(errorMsg);
        }
      }
    } catch (error: any) {
      console.error('Error creating school:', error);
      const errorMsg = error.message || 'An unexpected error occurred';
      setErrorMessage(errorMsg);

      // Call onError callback if provided
      if (onError) {
        onError(errorMsg);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto">
      {/* Success Alert */}
      {successMessage && (
        <div role="alert" className="alert alert-success mb-3 shadow-sm py-2">
          <Icon variant="check-circle" size={4} className="text-success" />
          <span className="font-medium text-sm">{successMessage}</span>
        </div>
      )}

      {/* Error Alert */}
      {errorMessage && (
        <div role="alert" className="alert alert-error mb-3 shadow-sm py-2">
          <Icon variant="x-circle" size={4} className="text-error" />
          <span className="font-medium text-sm">{errorMessage}</span>
        </div>
      )}

      {/* Form Card */}
      <div className="card bg-base-100 shadow-lg">
        <div className="card-body p-6">
          <h2 className="card-title text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
            <Icon variant="building-2" size={5} className="text-primary" />
            School Details
          </h2>

          <form onSubmit={handleSubmit(onSubmit)} className="w-full">{/* Form */}

            {/* Form Fields */}
            <div className="space-y-4 mb-6">
              {/* School Name Field - Full Width (Most Important) */}
              <div className="form-control">
                <label className="label py-1" htmlFor="name">
                  <span className="label-text text-sm font-medium text-gray-700">
                    School Name
                    <span className="text-xs font-normal text-red-500 ml-1">*</span>
                  </span>
                </label>
                <label className={cn(
                  'input input-bordered w-full flex items-center gap-2 h-11 rounded-lg transition-all duration-200',
                  errors.name
                    ? 'input-error border-red-400 focus-within:border-red-500 focus-within:ring-2 focus-within:ring-red-100'
                    : 'border-gray-200 focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-100 hover:border-blue-300'
                )}>
                  <Icon variant="building-2" size={4} className="text-gray-500" />
                  <input
                    id="name"
                    type="text"
                    placeholder="Springfield Elementary School"
                    className="grow text-sm bg-transparent w-full"
                    {...register('name')}
                    disabled={isSubmitting}
                  />
                </label>
                {errors.name && (
                  <div className="label py-0">
                    <span className="label-text-alt text-error font-medium text-xs">
                      {errors.name.message}
                    </span>
                  </div>
                )}
              </div>

              {/* Official Information Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* School Address Field */}
                <div className="form-control">
                  <label className="label py-1" htmlFor="address">
                    <span className="label-text text-sm font-medium text-gray-700">
                      School Address
                      <span className="text-xs font-normal text-red-500 ml-1">*</span>
                    </span>
                  </label>
                  <label className={cn(
                    'input input-bordered flex items-center gap-2 h-11 rounded-lg transition-all duration-200',
                    errors.address
                      ? 'input-error border-red-400 focus-within:border-red-500 focus-within:ring-2 focus-within:ring-red-100'
                      : 'border-gray-200 focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-100 hover:border-blue-300'
                  )}>
                    <Icon variant="map-pin" size={4} className="text-gray-500" />
                    <input
                      id="address"
                      type="text"
                      placeholder="123 Education St, Springfield"
                      className="grow text-sm bg-transparent"
                      {...register('address')}
                      disabled={isSubmitting}
                    />
                  </label>
                  {errors.address && (
                    <div className="label py-0">
                      <span className="label-text-alt text-error font-medium text-xs">
                        {errors.address.message}
                      </span>
                    </div>
                  )}
                </div>

                {/* Registration Number Field */}
                <div className="form-control">
                  <label className="label py-1" htmlFor="registeredNumber">
                    <span className="label-text text-sm font-medium text-gray-700">
                      Registration Number
                      <span className="text-xs font-normal text-red-500 ml-1">*</span>
                    </span>
                  </label>
                  <label className={cn(
                    'input input-bordered flex items-center gap-2 h-11 rounded-lg transition-all duration-200',
                    errors.registeredNumber
                      ? 'input-error border-red-400 focus-within:border-red-500 focus-within:ring-2 focus-within:ring-red-100'
                      : 'border-gray-200 focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-100 hover:border-blue-300'
                  )}>
                    <Icon variant="file-text" size={4} className="text-gray-500" />
                    <input
                      id="registeredNumber"
                      type="text"
                      placeholder="REG12345678"
                      className="grow text-sm bg-transparent"
                      {...register('registeredNumber')}
                      disabled={isSubmitting}
                    />
                  </label>
                  {errors.registeredNumber && (
                    <div className="label py-0">
                      <span className="label-text-alt text-error font-medium text-xs">
                        {errors.registeredNumber.message}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Contact Information Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* School Phone Field */}
                <div className="form-control">
                  <label className="label py-1" htmlFor="phoneNumber">
                    <span className="label-text text-sm font-medium text-gray-700">
                      School Phone
                      <span className="text-xs font-normal text-red-500 ml-1">*</span>
                    </span>
                  </label>
                  <label className={cn(
                    'input input-bordered flex items-center gap-2 h-11 rounded-lg transition-all duration-200',
                    errors.phoneNumber
                      ? 'input-error border-red-400 focus-within:border-red-500 focus-within:ring-2 focus-within:ring-red-100'
                      : 'border-gray-200 focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-100 hover:border-blue-300'
                  )}>
                    <Icon variant="phone" size={4} className="text-gray-500" />
                    <input
                      id="phoneNumber"
                      type="tel"
                      placeholder="******-123-4567"
                      className="grow text-sm bg-transparent"
                      {...register('phoneNumber')}
                      disabled={isSubmitting}
                    />
                  </label>
                  {errors.phoneNumber && (
                    <div className="label py-0">
                      <span className="label-text-alt text-error font-medium text-xs">
                        {errors.phoneNumber.message}
                      </span>
                    </div>
                  )}
                </div>

                {/* School Email Field */}
                <div className="form-control">
                  <label className="label py-1" htmlFor="email">
                    <span className="label-text text-sm font-medium text-gray-700">
                      School Email
                      <span className="text-xs font-normal text-red-500 ml-1">*</span>
                    </span>
                  </label>
                  <label className={cn(
                    'input input-bordered flex items-center gap-2 h-11 rounded-lg transition-all duration-200',
                    errors.email
                      ? 'input-error border-red-400 focus-within:border-red-500 focus-within:ring-2 focus-within:ring-red-100'
                      : 'border-gray-200 focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-100 hover:border-blue-300'
                  )}>
                    <Icon variant="mail" size={4} className="text-gray-500" />
                    <input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      className="grow text-sm bg-transparent"
                      {...register('email')}
                      disabled={isSubmitting}
                    />
                  </label>
                  {errors.email && (
                    <div className="label py-0">
                      <span className="label-text-alt text-error font-medium text-xs">
                        {errors.email.message}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>

              {/* Submit Button */}
              <div className="form-control flex justify-center">
                <Button
                  type="submit"
                  variant="primary"
                  className="w-auto px-8 h-11 text-sm font-semibold rounded-lg bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 border-0 shadow-md hover:shadow-lg hover:scale-105 transition-all duration-200 focus:ring-2 focus:ring-blue-200 disabled:opacity-70 disabled:cursor-not-allowed disabled:transform-none"
                  isLoading={isSubmitting}
                  disabled={isSubmitting}
                  iconProps={{
                    variant: "building-2",
                    size: 4,
                    className: "text-white"
                  }}
                >
                  {isSubmitting ? 'Creating School...' : 'Create School'}
                </Button>
              </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default SchoolCreationForm;
