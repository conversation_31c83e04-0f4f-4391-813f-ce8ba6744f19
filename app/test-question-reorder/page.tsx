'use client';

import React, { useState } from 'react';
import QuestionListingView, { Question } from '@/components/molecules/QuestionListingView/QuestionListingView';

// Sample test data
const sampleQuestions: Question[] = [
  {
    id: '1',
    type: 'multiple_choice',
    content: 'What is the capital of France?',
    options: ['London', 'Berlin', 'Paris', 'Madrid'],
    answer: ['Paris'],
    explain: 'Paris is the capital and largest city of France.',
    subject: 'Geography'
  },
  {
    id: '2',
    type: 'single_choice',
    content: 'Which planet is closest to the Sun?',
    options: ['Venus', 'Mercury', 'Earth', 'Mars'],
    answer: ['Mercury'],
    explain: 'Mercury is the smallest planet in our solar system and the closest to the Sun.',
    subject: 'Science'
  },
  {
    id: '3',
    type: 'fill_blank',
    content: 'The process of photosynthesis occurs in the _____ of plant cells.',
    options: ['chloroplasts', 'mitochondria', 'nucleus', 'cytoplasm'],
    answer: ['chloroplasts'],
    explain: 'Chloroplasts contain chlorophyll and are the sites where photosynthesis takes place.',
    subject: 'Biology'
  },
  {
    id: '4',
    type: 'multiple_choice',
    content: 'Which of the following are programming languages? (Select all that apply)',
    options: ['JavaScript', 'HTML', 'Python', 'CSS', 'Java'],
    answer: ['JavaScript', 'Python', 'Java'],
    explain: 'JavaScript, Python, and Java are programming languages. HTML and CSS are markup and styling languages respectively.',
    subject: 'Computer Science'
  },
  {
    id: '5',
    type: 'creative_writing',
    content: 'Write a short story about a time traveler who visits ancient Rome.',
    options: [],
    answer: ['Sample creative writing response about time travel to ancient Rome...'],
    explain: 'This is a creative writing exercise to develop storytelling skills and historical imagination.',
    prompt: 'Include details about what the time traveler sees, hears, and experiences in ancient Rome.',
    subject: 'Literature'
  }
];

const sampleQuestionIds = ['1', '2', '3', '4', '5'];

export default function TestQuestionReorderPage() {
  const [questions, setQuestions] = useState<Question[]>(sampleQuestions);
  const [questionIds, setQuestionIds] = useState<string[]>(sampleQuestionIds);
  const [isLoading, setIsLoading] = useState(false);
  const [lastSaveResult, setLastSaveResult] = useState<string | null>(null);

  // Mock save function to simulate API call
  const handleSaveReorder = async (reorderedQuestions: Question[], reorderedQuestionIds?: string[]) => {
    setIsLoading(true);
    setLastSaveResult(null);

    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Simulate random success/failure for testing error handling
      if (Math.random() > 0.8) {
        throw new Error('Simulated network error - please try again');
      }

      // Update state with new order
      setQuestions(reorderedQuestions);
      if (reorderedQuestionIds) {
        setQuestionIds(reorderedQuestionIds);
      }

      setLastSaveResult('✅ Questions reordered successfully!');
      
      // Clear success message after 3 seconds
      setTimeout(() => setLastSaveResult(null), 3000);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to save question order';
      setLastSaveResult(`❌ Error: ${errorMessage}`);
      throw error; // Re-throw to let the component handle it
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Question Reorder Test Page
          </h1>
          <p className="text-gray-600">
            Test the new drag & drop question reordering functionality. Click "Reorder Questions" to open the modal.
          </p>
          
          {/* Status Display */}
          {lastSaveResult && (
            <div className={`mt-4 p-3 rounded-lg ${
              lastSaveResult.includes('✅') 
                ? 'bg-green-50 text-green-700 border border-green-200' 
                : 'bg-red-50 text-red-700 border border-red-200'
            }`}>
              {lastSaveResult}
            </div>
          )}
        </div>

        {/* Test Instructions */}
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h2 className="text-lg font-semibold text-blue-900 mb-2">Test Instructions:</h2>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Click "Reorder Questions" button to open the reorder modal</li>
            <li>• Drag questions by their handle (≡ icon) to reorder them</li>
            <li>• Test on both desktop and mobile devices</li>
            <li>• Try keyboard navigation (Tab, Arrow keys, Space/Enter)</li>
            <li>• Test the Cancel and Save functionality</li>
            <li>• The save operation has a 20% chance of simulated failure for error testing</li>
          </ul>
        </div>

        {/* Question Listing with Reorder Functionality */}
        <QuestionListingView
          questions={questions}
          questionIds={questionIds}
          allowReordering={true}
          onSaveReorder={handleSaveReorder}
          isReordering={isLoading}
          worksheetInfo={{
            subject: 'Mixed Subjects',
            topic: 'General Knowledge Test',
            grade: 'Grade 10',
            language: 'English',
            level: 'Intermediate',
            totalQuestions: questions.length
          }}
          containerClass="bg-white rounded-lg shadow-sm"
        />

        {/* Debug Info */}
        <div className="mt-8 p-4 bg-gray-100 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Debug Information:</h3>
          <div className="text-sm text-gray-700">
            <p><strong>Current Question Order:</strong></p>
            <ol className="list-decimal list-inside mt-1 space-y-1">
              {questions.map((q, index) => (
                <li key={q.id}>
                  <span className="font-medium">ID: {q.id}</span> - {q.content.substring(0, 50)}...
                </li>
              ))}
            </ol>
            <p className="mt-3"><strong>Question IDs Array:</strong> [{questionIds.join(', ')}]</p>
            <p className="mt-1"><strong>Loading State:</strong> {isLoading ? 'Loading...' : 'Ready'}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
